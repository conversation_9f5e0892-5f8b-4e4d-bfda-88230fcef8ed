namespace Kantoku.Application.Dtos.Requests;

/// <summary>
/// DTO for request response
/// </summary>
public class RequestDto
{
    public Guid Id { get; set; }
    public Guid EmployeeId { get; set; }
    public string RequestType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Priority { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateOnly? StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
    public decimal? Amount { get; set; }
    public string? Currency { get; set; }
    public Dictionary<string, object> AdditionalDetails { get; set; } = new();
    public List<RequestApprovalDto> Approvals { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// DTO for request approval
/// </summary>
public class RequestApprovalDto
{
    public Guid Id { get; set; }
    public Guid ApproverId { get; set; }
    public int Order { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Comments { get; set; }
    public DateTime? ApprovedAt { get; set; }
}
