namespace Kantoku.Application.Services;

/// <summary>
/// Interface for notification service
/// </summary>
public interface INotificationService
{
    /// <summary>
    /// Sends a notification to a single user
    /// </summary>
    /// <param name="userId">Target user ID</param>
    /// <param name="title">Notification title</param>
    /// <param name="message">Notification message</param>
    /// <param name="type">Notification type</param>
    /// <param name="priority">Notification priority</param>
    /// <param name="channels">Delivery channels</param>
    /// <param name="data">Additional data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Notification ID</returns>
    Task<Guid> SendNotificationAsync(
        Guid userId,
        string title,
        string message,
        NotificationType type = NotificationType.Info,
        NotificationPriority priority = NotificationPriority.Normal,
        IEnumerable<DeliveryChannel>? channels = null,
        Dictionary<string, object>? data = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends a notification to multiple users
    /// </summary>
    /// <param name="userIds">Target user IDs</param>
    /// <param name="title">Notification title</param>
    /// <param name="message">Notification message</param>
    /// <param name="type">Notification type</param>
    /// <param name="priority">Notification priority</param>
    /// <param name="channels">Delivery channels</param>
    /// <param name="data">Additional data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of notification IDs</returns>
    Task<IEnumerable<Guid>> SendNotificationAsync(
        IEnumerable<Guid> userIds,
        string title,
        string message,
        NotificationType type = NotificationType.Info,
        NotificationPriority priority = NotificationPriority.Normal,
        IEnumerable<DeliveryChannel>? channels = null,
        Dictionary<string, object>? data = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends a notification to users by role
    /// </summary>
    /// <param name="roleId">Target role ID</param>
    /// <param name="title">Notification title</param>
    /// <param name="message">Notification message</param>
    /// <param name="type">Notification type</param>
    /// <param name="priority">Notification priority</param>
    /// <param name="channels">Delivery channels</param>
    /// <param name="data">Additional data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of notification IDs</returns>
    Task<IEnumerable<Guid>> SendNotificationToRoleAsync(
        Guid roleId,
        string title,
        string message,
        NotificationType type = NotificationType.Info,
        NotificationPriority priority = NotificationPriority.Normal,
        IEnumerable<DeliveryChannel>? channels = null,
        Dictionary<string, object>? data = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Schedules a notification for future delivery
    /// </summary>
    /// <param name="userId">Target user ID</param>
    /// <param name="title">Notification title</param>
    /// <param name="message">Notification message</param>
    /// <param name="scheduledAt">Scheduled delivery time</param>
    /// <param name="type">Notification type</param>
    /// <param name="priority">Notification priority</param>
    /// <param name="channels">Delivery channels</param>
    /// <param name="data">Additional data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Notification ID</returns>
    Task<Guid> ScheduleNotificationAsync(
        Guid userId,
        string title,
        string message,
        DateTime scheduledAt,
        NotificationType type = NotificationType.Info,
        NotificationPriority priority = NotificationPriority.Normal,
        IEnumerable<DeliveryChannel>? channels = null,
        Dictionary<string, object>? data = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Marks a notification as read
    /// </summary>
    /// <param name="notificationId">Notification ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if marked successfully</returns>
    Task<bool> MarkAsReadAsync(Guid notificationId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancels a scheduled notification
    /// </summary>
    /// <param name="notificationId">Notification ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if cancelled successfully</returns>
    Task<bool> CancelNotificationAsync(Guid notificationId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Notification type enumeration
/// </summary>
public enum NotificationType
{
    Info,
    Warning,
    Error,
    Success,
    Reminder,
    Alert,
    Announcement
}

/// <summary>
/// Notification priority enumeration
/// </summary>
public enum NotificationPriority
{
    Low,
    Normal,
    High,
    Urgent,
    Critical
}

/// <summary>
/// Delivery channel enumeration
/// </summary>
public enum DeliveryChannel
{
    InApp,
    Email,
    SMS,
    Push,
    Webhook,
    Slack,
    Teams
}
