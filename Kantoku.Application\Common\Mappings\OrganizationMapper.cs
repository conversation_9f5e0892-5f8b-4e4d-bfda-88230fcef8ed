using Kantoku.Application.Dtos.Organizations;
using Kantoku.Domain.OrganizationManagement;

namespace Kantoku.Application.Common.Mappings;

/// <summary>
/// Manual mapper for Organization domain entity to DTOs
/// </summary>
public static class OrganizationMapper
{
    /// <summary>
    /// Maps Organization domain entity to OrganizationDto
    /// </summary>
    /// <param name="organization">Organization domain entity</param>
    /// <returns>OrganizationDto</returns>
    public static OrganizationDto ToDto(this Organization organization)
    {
        return new OrganizationDto
        {
            Id = organization.Id.Value,
            Name = organization.Name,
            Description = organization.Description,
            Address = organization.Address?.Address,
            PostalCode = organization.Address?.PostalCode,
            Phone = organization.ContactInfo?.Phone,
            Email = organization.ContactInfo?.Email,
            Website = organization.Website,
            TaxNumber = organization.TaxNumber,
            RegistrationNumber = organization.RegistrationNumber,
            CreatedAt = organization.CreatedAt,
            UpdatedAt = organization.UpdatedAt
        };
    }

    /// <summary>
    /// Maps a collection of Organization entities to DTOs
    /// </summary>
    /// <param name="organizations">Collection of Organization entities</param>
    /// <returns>Collection of OrganizationDto</returns>
    public static IEnumerable<OrganizationDto> ToDto(this IEnumerable<Organization> organizations)
    {
        return organizations.Select(org => org.ToDto());
    }

    /// <summary>
    /// Maps a list of Organization entities to DTOs
    /// </summary>
    /// <param name="organizations">List of Organization entities</param>
    /// <returns>List of OrganizationDto</returns>
    public static List<OrganizationDto> ToDto(this List<Organization> organizations)
    {
        return organizations.Select(org => org.ToDto()).ToList();
    }

    /// <summary>
    /// Maps a readonly list of Organization entities to DTOs
    /// </summary>
    /// <param name="organizations">Readonly list of Organization entities</param>
    /// <returns>Readonly list of OrganizationDto</returns>
    public static IReadOnlyList<OrganizationDto> ToDto(this IReadOnlyList<Organization> organizations)
    {
        return organizations.Select(org => org.ToDto()).ToList().AsReadOnly();
    }
}
