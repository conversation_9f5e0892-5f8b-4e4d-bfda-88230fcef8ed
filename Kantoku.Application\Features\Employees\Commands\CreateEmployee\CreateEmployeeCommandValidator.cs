using FluentValidation;

namespace Kantoku.Application.Features.Employees.Commands.CreateEmployee;

/// <summary>
/// Validator for CreateEmployeeCommand
/// </summary>
public class CreateEmployeeCommandValidator : AbstractValidator<CreateEmployeeCommand>
{
    public CreateEmployeeCommandValidator()
    {
        RuleFor(x => x.AccountId)
            .NotEmpty()
            .WithMessage("Account ID is required");

        RuleFor(x => x.OrganizationId)
            .NotEmpty()
            .WithMessage("Organization ID is required");

        RuleFor(x => x.EmployeeCode)
            .NotEmpty()
            .WithMessage("Employee code is required")
            .MaximumLength(50)
            .WithMessage("Employee code must not exceed 50 characters");

        RuleFor(x => x.FirstName)
            .NotEmpty()
            .WithMessage("First name is required")
            .MaximumLength(100)
            .WithMessage("First name must not exceed 100 characters");

        RuleFor(x => x.LastName)
            .NotEmpty()
            .WithMessage("Last name is required")
            .MaximumLength(100)
            .WithMessage("Last name must not exceed 100 characters");

        RuleFor(x => x.MiddleName)
            .MaximumLength(100)
            .WithMessage("Middle name must not exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.MiddleName));

        RuleFor(x => x.MonthlySalary)
            .GreaterThan(0)
            .WithMessage("Monthly salary must be greater than 0");

        RuleFor(x => x.DailySalary)
            .GreaterThan(0)
            .WithMessage("Daily salary must be greater than 0")
            .When(x => x.DailySalary.HasValue);

        RuleFor(x => x.HourlySalary)
            .GreaterThan(0)
            .WithMessage("Hourly salary must be greater than 0")
            .When(x => x.HourlySalary.HasValue);

        RuleFor(x => x.EmployeeType)
            .NotEmpty()
            .WithMessage("Employee type is required")
            .Must(BeValidEmployeeType)
            .WithMessage("Invalid employee type. Valid types are: Officer, Worker, Contractor, Intern, PartTime");

        RuleFor(x => x.Email)
            .EmailAddress()
            .WithMessage("Invalid email format")
            .When(x => !string.IsNullOrEmpty(x.Email));

        RuleFor(x => x.Phone)
            .Matches(@"^[\d\-\+\(\)\s]+$")
            .WithMessage("Invalid phone number format")
            .When(x => !string.IsNullOrEmpty(x.Phone));
    }

    private static bool BeValidEmployeeType(string employeeType)
    {
        var validTypes = new[] { "Officer", "Worker", "Contractor", "Intern", "PartTime" };
        return validTypes.Contains(employeeType);
    }
}
