namespace Kantoku.Application.Dtos.Employees;

/// <summary>
/// DTO for employee response
/// </summary>
public class EmployeeDto
{
    public Guid Id { get; set; }
    public Guid AccountId { get; set; }
    public Guid OrganizationId { get; set; }
    public string EmployeeCode { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? MiddleName { get; set; }
    public string FullName { get; set; } = string.Empty;
    public decimal MonthlySalary { get; set; }
    public decimal? DailySalary { get; set; }
    public decimal? HourlySalary { get; set; }
    public string EmployeeType { get; set; } = string.Empty;
    public string WorkingStatus { get; set; } = string.Empty;
    public Guid? StructureId { get; set; }
    public Guid? PositionId { get; set; }
    public Guid? RankingId { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public bool IsOrgAdmin { get; set; }
    public bool IsOrgOwner { get; set; }
    public bool HasApprovalAuthority { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
