using Kantoku.Application.Common.Mappings;
using Kantoku.Application.Common.Models;
using Kantoku.Application.Interfaces;
using Kantoku.Domain.EmployeeManagement;
using Kantoku.Domain.RequestManagement;
using Kantoku.Domain.RequestManagement.Enums;
using Kantoku.Domain.RequestManagement.ValueObjects;
using Kantoku.SharedKernel.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Kantoku.Application.Features.Requests.Commands.CreateRequest;

/// <summary>
/// Handler for CreateRequestCommand
/// </summary>
public class CreateRequestCommandHandler : IRequestHandler<CreateRequestCommand, BaseResponse<RequestDto>>
{
    private readonly IRequestRepository _requestRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreateRequestCommandHandler> _logger;

    public CreateRequestCommandHandler(
        IRequestRepository requestRepository,
        IUnitOfWork unitOfWork,
        ILogger<CreateRequestCommandHandler> logger)
    {
        _requestRepository = requestRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<BaseResponse<RequestDto>> Handle(CreateRequestCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Parse enums
            var requestType = RequestType.FromName(request.RequestType);
            var priority = RequestPriority.FromName(request.Priority);

            // Create value objects
            RequestDates? requestDates = null;
            if (request.StartDate.HasValue || request.EndDate.HasValue)
            {
                requestDates = RequestDates.Create(request.StartDate, request.EndDate);
            }

            Money? amount = null;
            if (request.Amount.HasValue)
            {
                amount = Money.Create(request.Amount.Value, request.Currency ?? "JPY");
            }

            var requestDetails = RequestDetails.Create(
                requestType,
                request.AdditionalDetails,
                amount);

            // Create request aggregate
            var newRequest = new Request(
                RequestId.New(),
                EmployeeId.From(request.EmployeeId),
                requestType,
                request.Title,
                priority,
                requestDetails,
                requestDates,
                request.Description);

            // Add approvers
            for (int i = 0; i < request.ApproverIds.Count; i++)
            {
                newRequest.AddApprover(request.ApproverIds[i], i + 1);
            }

            // Submit the request
            newRequest.Submit();

            // Save to repository
            await _requestRepository.AddAsync(newRequest, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Map to DTO using manual mapper
            var requestDto = newRequest.ToDto();

            _logger.LogInformation("Request created successfully with ID: {RequestId}", newRequest.Id.Value);

            return BaseResponse<RequestDto>.Success(requestDto, "Request created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating request: {ErrorMessage}", ex.Message);
            return BaseResponse<RequestDto>.Failure("Failed to create request");
        }
    }
}
