namespace Kantoku.Application.Dtos.Projects;

/// <summary>
/// DTO for project response
/// </summary>
public class ProjectDto
{
    public Guid Id { get; set; }
    public Guid OrganizationId { get; set; }
    public string ProjectCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateOnly ExpectedStartDate { get; set; }
    public DateOnly ExpectedEndDate { get; set; }
    public DateOnly? ActualStartDate { get; set; }
    public DateOnly? ActualEndDate { get; set; }
    public decimal? EstimatedBudget { get; set; }
    public decimal? ActualCost { get; set; }
    public string Status { get; set; } = string.Empty;
    public Guid? CustomerId { get; set; }
    public Guid? ContractorId { get; set; }
    public Guid? ProjectTypeId { get; set; }
    public List<Guid> ManagerIds { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
