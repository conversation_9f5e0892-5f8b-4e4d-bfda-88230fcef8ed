using Kantoku.Application.Common.Models;
using Kantoku.Application.Dtos.Projects;
using Kantoku.Application.Interfaces;

namespace Kantoku.Application.Features.Projects.Commands.CreateProject;

/// <summary>
/// Command to create a new project
/// </summary>
public class CreateProjectCommand : ICommand<BaseResponse<ProjectDto>>
{
    public Guid OrganizationId { get; set; }
    public string ProjectCode { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateOnly ExpectedStartDate { get; set; }
    public DateOnly ExpectedEndDate { get; set; }
    public decimal? EstimatedBudget { get; set; }
    public Guid? CustomerId { get; set; }
    public Guid? ContractorId { get; set; }
    public Guid? ProjectTypeId { get; set; }
    public List<Guid> ManagerIds { get; set; } = new();
}
