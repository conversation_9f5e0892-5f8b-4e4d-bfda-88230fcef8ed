using Kantoku.Application.Common.Mappings;
using Kantoku.Application.Common.Models;
using Kantoku.Application.Dtos.Employees;
using Kantoku.Domain.EmployeeManagement;
using Kantoku.Domain.Specifications;
using Kantoku.SharedKernel.Pagination;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Kantoku.Application.Features.Employees.Queries.GetEmployeesByFilter;

/// <summary>
/// Handler for GetEmployeesByFilterQuery
/// </summary>
public class GetEmployeesByFilterQueryHandler : IRequestHandler<GetEmployeesByFilterQuery, BaseResponse<PaginatedResponse<EmployeeDto>>>
{
    private readonly IEmployeeRepository _employeeRepository;
    private readonly ILogger<GetEmployeesByFilterQueryHandler> _logger;

    public GetEmployeesByFilterQueryHandler(
        IEmployeeRepository employeeRepository,
        ILogger<GetEmployeesByFilterQueryHandler> logger)
    {
        _employeeRepository = employeeRepository;
        _logger = logger;
    }

    public async Task<BaseResponse<PaginatedResponse<EmployeeDto>>> Handle(GetEmployeesByFilterQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Build specification based on filters
            var specification = EmployeeSpecifications.All();

            if (request.OrganizationId.HasValue)
            {
                specification = specification.And(EmployeeSpecifications.ByOrganization(request.OrganizationId.Value));
            }

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                specification = specification.And(EmployeeSpecifications.BySearchTerm(request.SearchTerm));
            }

            if (!string.IsNullOrEmpty(request.EmployeeType))
            {
                specification = specification.And(EmployeeSpecifications.ByEmployeeType(request.EmployeeType));
            }

            if (!string.IsNullOrEmpty(request.WorkingStatus))
            {
                specification = specification.And(EmployeeSpecifications.ByWorkingStatus(request.WorkingStatus));
            }

            if (request.StructureId.HasValue)
            {
                specification = specification.And(EmployeeSpecifications.ByStructure(request.StructureId.Value));
            }

            if (request.PositionId.HasValue)
            {
                specification = specification.And(EmployeeSpecifications.ByPosition(request.PositionId.Value));
            }

            if (request.RankingId.HasValue)
            {
                specification = specification.And(EmployeeSpecifications.ByRanking(request.RankingId.Value));
            }

            if (request.IsOrgAdmin.HasValue)
            {
                specification = specification.And(EmployeeSpecifications.ByOrgAdmin(request.IsOrgAdmin.Value));
            }

            if (request.HasApprovalAuthority.HasValue)
            {
                specification = specification.And(EmployeeSpecifications.ByApprovalAuthority(request.HasApprovalAuthority.Value));
            }

            // Create pagination request
            var pageRequest = new PagedRequest
            {
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };

            // Add sorting if specified
            if (!string.IsNullOrEmpty(request.SortBy))
            {
                pageRequest.SortCriteria.Add(new SortCriteria
                {
                    PropertyName = request.SortBy,
                    Descending = request.SortDescending
                });
            }

            // Get paginated results
            var pagedResult = await _employeeRepository.GetPagedAsync(specification, pageRequest, cancellationToken);

            // Map to DTOs using manual mapper
            var employeeDtos = pagedResult.Items.ToDto();

            var response = PaginatedResponse<EmployeeDto>.Create(
                employeeDtos,
                pagedResult.PageNumber,
                pagedResult.PageSize,
                pagedResult.TotalCount);

            return BaseResponse<PaginatedResponse<EmployeeDto>>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving employees with filter");
            return BaseResponse<PaginatedResponse<EmployeeDto>>.Failure("Failed to retrieve employees");
        }
    }
}
