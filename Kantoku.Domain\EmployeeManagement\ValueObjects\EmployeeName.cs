using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.EmployeeManagement.ValueObjects;

/// <summary>
/// Value object representing an employee's name
/// </summary>
public class EmployeeName : ValueObject
{
    public string FullName { get; private set; } = null!;
    public string? FirstName { get; private set; }
    public string? LastName { get; private set; }
    public string? MiddleName { get; private set; }

    private EmployeeName() { } // For EF Core

    public EmployeeName(string fullName, string? firstName = null, string? lastName = null, string? middleName = null)
    {
        SetFullName(fullName);
        FirstName = firstName?.Trim();
        LastName = lastName?.Trim();
        MiddleName = middleName?.Trim();
    }

    public static EmployeeName Create(string fullName)
    {
        return new EmployeeName(fullName);
    }

    public static EmployeeName Create(string firstName, string lastName, string? middleName = null)
    {
        var fullName = string.IsNullOrEmpty(middleName)
            ? $"{firstName} {lastName}"
            : $"{firstName} {middleName} {lastName}";

        return new EmployeeName(fullName, firstName, lastName, middleName);
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return FullName;
        yield return FirstName;
        yield return LastName;
        yield return MiddleName;
    }

    private void SetFullName(string fullName)
    {
        if (string.IsNullOrWhiteSpace(fullName))
            throw new ArgumentException("Employee name cannot be null or empty", nameof(fullName));

        if (fullName.Length > 200)
            throw new ArgumentException("Employee name cannot exceed 200 characters", nameof(fullName));

        FullName = fullName.Trim();
    }

    public override string ToString() => FullName;
}
