using Kantoku.Application.Common.Mappings;
using Kantoku.Application.Common.Models;
using Kantoku.Application.Interfaces;
using Kantoku.Domain.AccountManagement;
using Kantoku.Domain.EmployeeManagement;
using Kantoku.Domain.EmployeeManagement.ValueObjects;
using Kantoku.Domain.OrganizationManagement;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Kantoku.Application.Features.Employees.Commands.CreateEmployee;

/// <summary>
/// Handler for CreateEmployeeCommand
/// </summary>
public class CreateEmployeeCommandHandler : IRequestHandler<CreateEmployeeCommand, BaseResponse<EmployeeDto>>
{
    private readonly IEmployeeRepository _employeeRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreateEmployeeCommandHandler> _logger;

    public CreateEmployeeCommandHandler(
        IEmployeeRepository employeeRepository,
        IUnitOfWork unitOfWork,
        ILogger<CreateEmployeeCommandHandler> logger)
    {
        _employeeRepository = employeeRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<BaseResponse<EmployeeDto>> Handle(CreateEmployeeCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Create value objects
            var employeeName = EmployeeName.Create(request.FirstName, request.LastName, request.MiddleName);
            var salaryInfo = SalaryInfo.CreateMonthly(request.MonthlySalary, request.DailySalary, request.HourlySalary);

            // Parse employee type
            var employeeType = Domain.EmployeeAggregate.EmployeeTypeEnum.FromName(request.EmployeeType);

            // Create employee aggregate
            var employee = new Employee(
                EmployeeId.New(),
                AccountId.From(request.AccountId),
                OrganizationId.From(request.OrganizationId),
                request.EmployeeCode,
                employeeName,
                salaryInfo);

            // Set organizational assignment if provided
            if (request.StructureId.HasValue && request.PositionId.HasValue && request.RankingId.HasValue)
            {
                employee.UpdateOrganizationalAssignment(
                    StructureId.From(request.StructureId.Value),
                    PositionId.From(request.PositionId.Value),
                    RankingId.From(request.RankingId.Value));
            }

            // Set contact information if provided
            if (!string.IsNullOrEmpty(request.Email) || !string.IsNullOrEmpty(request.Phone))
            {
                var contactInfo = EmployeeContactInfo.Create();

                if (!string.IsNullOrEmpty(request.Email))
                {
                    contactInfo.AddEmail(request.Email, true); // Set as primary
                }

                if (!string.IsNullOrEmpty(request.Phone))
                {
                    contactInfo.AddPhone(request.Phone, true); // Set as primary
                }

                employee.UpdateContactInfo(contactInfo);
            }

            // Set admin privileges
            if (request.IsOrgAdmin)
            {
                employee.GrantOrgAdminPrivileges();
            }

            if (request.IsOrgOwner)
            {
                employee.GrantOrgOwnerPrivileges();
            }

            // Save to repository
            await _employeeRepository.AddAsync(employee, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Map to DTO using manual mapper
            var employeeDto = employee.ToDto();

            _logger.LogInformation("Employee created successfully with ID: {EmployeeId}", employee.Id.Value);

            return BaseResponse<EmployeeDto>.Success(employeeDto, "Employee created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating employee: {ErrorMessage}", ex.Message);
            return BaseResponse<EmployeeDto>.Failure("Failed to create employee");
        }
    }
}
