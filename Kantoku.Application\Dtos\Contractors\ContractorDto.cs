namespace Kantoku.Application.Dtos.Contractors;

/// <summary>
/// DTO for contractor response
/// </summary>
public class ContractorDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? Address { get; set; }
    public string? PostalCode { get; set; }
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public string? Website { get; set; }
    public string? TaxNumber { get; set; }
    public string? RegistrationNumber { get; set; }
    public string ContractorType { get; set; } = string.Empty;
    public List<ContractorContactDto> Contacts { get; set; } = new();
    public List<ContractorCertificationDto> Certifications { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// DTO for contractor contact
/// </summary>
public class ContractorContactDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Position { get; set; }
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public bool IsPrimary { get; set; }
}

/// <summary>
/// DTO for contractor certification
/// </summary>
public class ContractorCertificationDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? IssuingAuthority { get; set; }
    public DateOnly IssueDate { get; set; }
    public DateOnly? ExpiryDate { get; set; }
    public string? CertificateNumber { get; set; }
}
