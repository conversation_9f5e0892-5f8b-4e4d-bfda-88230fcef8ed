using Kantoku.Application.Common.Models;
using Kantoku.Application.Dtos.Employees;
using Kantoku.Application.Interfaces;

namespace Kantoku.Application.Features.Employees.Commands.CreateEmployee;

/// <summary>
/// Command to create a new employee
/// </summary>
public class CreateEmployeeCommand : ICommand<BaseResponse<EmployeeDto>>
{
    public Guid AccountId { get; set; }
    public Guid OrganizationId { get; set; }
    public string EmployeeCode { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? MiddleName { get; set; }
    public decimal MonthlySalary { get; set; }
    public decimal? DailySalary { get; set; }
    public decimal? HourlySalary { get; set; }
    public string EmployeeType { get; set; } = "Officer"; // Officer, Worker, Contractor, Intern, PartTime
    public Guid? StructureId { get; set; }
    public Guid? PositionId { get; set; }
    public Guid? RankingId { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public bool IsOrgAdmin { get; set; }
    public bool IsOrgOwner { get; set; }
}
