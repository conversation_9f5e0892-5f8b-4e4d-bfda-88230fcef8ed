using Kantoku.Application.Common.Mappings;
using Kantoku.Application.Common.Models;
using Kantoku.Application.Dtos.Organizations;
using Kantoku.Domain.OrganizationManagement;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Kantoku.Application.Features.Organizations.Queries.GetOrganizationById;

/// <summary>
/// Handler for GetOrganizationByIdQuery
/// </summary>
public class GetOrganizationByIdQueryHandler : IRequestHandler<GetOrganizationByIdQuery, BaseResponse<OrganizationDto>>
{
    private readonly IOrganizationRepository _organizationRepository;
    private readonly ILogger<GetOrganizationByIdQueryHandler> _logger;

    public GetOrganizationByIdQueryHandler(
        IOrganizationRepository organizationRepository,
        ILogger<GetOrganizationByIdQueryHandler> logger)
    {
        _organizationRepository = organizationRepository;
        _logger = logger;
    }

    public async Task<BaseResponse<OrganizationDto>> Handle(GetOrganizationByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var organizationId = OrganizationId.From(request.OrganizationId);
            var organization = await _organizationRepository.GetByIdAsync(organizationId, cancellationToken);

            if (organization == null)
            {
                _logger.LogWarning("Organization not found with ID: {OrganizationId}", request.OrganizationId);
                return BaseResponse<OrganizationDto>.Failure("Organization not found");
            }

            var organizationDto = organization.ToDto();

            return BaseResponse<OrganizationDto>.Success(organizationDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving organization with ID: {OrganizationId}", request.OrganizationId);
            return BaseResponse<OrganizationDto>.Failure("Failed to retrieve organization");
        }
    }
}
