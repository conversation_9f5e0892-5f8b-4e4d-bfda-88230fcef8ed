using Kantoku.Application.Common.Mappings;
using Kantoku.Application.Common.Models;
using Kantoku.Application.Interfaces;
using Kantoku.Domain.CustomerManagement;
using Kantoku.Domain.ContractorManagement;
using Kantoku.Domain.OrganizationManagement;
using Kantoku.Domain.ProjectManagement;
using Kantoku.Domain.ProjectManagement.ValueObjects;
using Kantoku.SharedKernel.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Kantoku.Application.Features.Projects.Commands.CreateProject;

/// <summary>
/// Handler for CreateProjectCommand
/// </summary>
public class CreateProjectCommandHandler : IRequestHandler<CreateProjectCommand, BaseResponse<ProjectDto>>
{
    private readonly IProjectRepository _projectRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreateProjectCommandHandler> _logger;

    public CreateProjectCommandHandler(
        IProjectRepository projectRepository,
        IUnitOfWork unitOfWork,
        ILogger<CreateProjectCommandHandler> logger)
    {
        _projectRepository = projectRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<BaseResponse<ProjectDto>> Handle(CreateProjectCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Create value objects
            var projectDates = ProjectDates.CreateExpected(request.ExpectedStartDate, request.ExpectedEndDate);

            ProjectBudget? projectBudget = null;
            if (request.EstimatedBudget.HasValue)
            {
                projectBudget = ProjectBudget.CreateEstimated(Money.Create(request.EstimatedBudget.Value, "JPY"));
            }

            // Create project aggregate
            var project = new Project(
                ProjectId.New(),
                OrganizationId.From(request.OrganizationId),
                request.ProjectCode,
                request.Name,
                projectDates,
                request.Description);

            // Set budget if provided
            if (projectBudget != null)
            {
                project.UpdateBudget(projectBudget);
            }

            // Set customer if provided
            if (request.CustomerId.HasValue)
            {
                project.AssignCustomer(CustomerId.From(request.CustomerId.Value));
            }

            // Set contractor if provided
            if (request.ContractorId.HasValue)
            {
                project.AssignContractor(ContractorId.From(request.ContractorId.Value));
            }

            // Set project type if provided
            if (request.ProjectTypeId.HasValue)
            {
                project.UpdateProjectType(ProjectTypeId.From(request.ProjectTypeId.Value));
            }

            // Add managers
            foreach (var managerId in request.ManagerIds)
            {
                project.AddManager(ProjectManagerId.New(), managerId);
            }

            // Save to repository
            await _projectRepository.AddAsync(project, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Map to DTO using manual mapper
            var projectDto = project.ToDto();

            _logger.LogInformation("Project created successfully with ID: {ProjectId}", project.Id.Value);

            return BaseResponse<ProjectDto>.Success(projectDto, "Project created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating project: {ErrorMessage}", ex.Message);
            return BaseResponse<ProjectDto>.Failure("Failed to create project");
        }
    }
}
