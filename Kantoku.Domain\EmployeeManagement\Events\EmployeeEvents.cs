using Kantoku.Domain.EmployeeManagement.Enums;
using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.EmployeeManagement.Events;

/// <summary>
/// Domain event raised when an employee is created
/// </summary>
public class EmployeeCreatedEvent : DomainEvent
{
    public Employee Employee { get; }
    public DateTime OccurredOn { get; }

    public EmployeeCreatedEvent(Employee employee)
    {
        Employee = employee;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an employee is updated
/// </summary>
public class EmployeeUpdatedEvent : DomainEvent
{
    public Employee Employee { get; }
    public DateTime OccurredOn { get; }

    public EmployeeUpdatedEvent(Employee employee)
    {
        Employee = employee;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an employee's organizational assignment changes
/// </summary>
public class EmployeeOrganizationalAssignmentChangedEvent : DomainEvent
{
    public Employee Employee { get; }
    public Guid? OldStructureId { get; }
    public Guid? OldPositionId { get; }
    public Guid? OldRankingId { get; }
    public DateTime OccurredOn { get; }

    public EmployeeOrganizationalAssignmentChangedEvent(
        Employee employee,
        Guid? oldStructureId,
        Guid? oldPositionId,
        Guid? oldRankingId)
    {
        Employee = employee;
        OldStructureId = oldStructureId;
        OldPositionId = oldPositionId;
        OldRankingId = oldRankingId;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an employee's working status changes
/// </summary>
public class EmployeeWorkingStatusChangedEvent : DomainEvent
{
    public Employee Employee { get; }
    public WorkingStatus OldStatus { get; }
    public WorkingStatus NewStatus { get; }
    public DateTime OccurredOn { get; }

    public EmployeeWorkingStatusChangedEvent(Employee employee, WorkingStatus oldStatus, WorkingStatus newStatus)
    {
        Employee = employee;
        OldStatus = oldStatus;
        NewStatus = newStatus;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an employee's salary is updated
/// </summary>
public class EmployeeSalaryUpdatedEvent : DomainEvent
{
    public Employee Employee { get; }
    public DateTime OccurredOn { get; }

    public EmployeeSalaryUpdatedEvent(Employee employee)
    {
        Employee = employee;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an employee is promoted to organization admin
/// </summary>
public class EmployeePromotedToOrgAdminEvent : DomainEvent
{
    public Employee Employee { get; }
    public DateTime OccurredOn { get; }

    public EmployeePromotedToOrgAdminEvent(Employee employee)
    {
        Employee = employee;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an employee's organization admin privileges are removed
/// </summary>
public class EmployeeOrgAdminPrivilegesRemovedEvent : DomainEvent
{
    public Employee Employee { get; }
    public DateTime OccurredOn { get; }

    public EmployeeOrgAdminPrivilegesRemovedEvent(Employee employee)
    {
        Employee = employee;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an employee is granted approval authority
/// </summary>
public class EmployeeApprovalAuthorityGrantedEvent : DomainEvent
{
    public Employee Employee { get; }
    public DateTime OccurredOn { get; }

    public EmployeeApprovalAuthorityGrantedEvent(Employee employee)
    {
        Employee = employee;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an employee's approval authority is revoked
/// </summary>
public class EmployeeApprovalAuthorityRevokedEvent : DomainEvent
{
    public Employee Employee { get; }
    public DateTime OccurredOn { get; }

    public EmployeeApprovalAuthorityRevokedEvent(Employee employee)
    {
        Employee = employee;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an employee is deactivated
/// </summary>
public class EmployeeDeactivatedEvent : DomainEvent
{
    public Employee Employee { get; }
    public DateTime OccurredOn { get; }

    public EmployeeDeactivatedEvent(Employee employee)
    {
        Employee = employee;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when an employee is reactivated
/// </summary>
public class EmployeeReactivatedEvent : DomainEvent
{
    public Employee Employee { get; }
    public DateTime OccurredOn { get; }

    public EmployeeReactivatedEvent(Employee employee)
    {
        Employee = employee;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a role is added to an employee
/// </summary>
public class EmployeeRoleAddedEvent : DomainEvent
{
    public Employee Employee { get; }
    public EmployeeRole Role { get; }
    public DateTime OccurredOn { get; }

    public EmployeeRoleAddedEvent(Employee employee, EmployeeRole role)
    {
        Employee = employee;
        Role = role;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a role is removed from an employee
/// </summary>
public class EmployeeRoleRemovedEvent : DomainEvent
{
    public Employee Employee { get; }
    public EmployeeRole Role { get; }
    public DateTime OccurredOn { get; }

    public EmployeeRoleRemovedEvent(Employee employee, EmployeeRole role)
    {
        Employee = employee;
        Role = role;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Domain event raised when a leave is added to an employee
/// </summary>
public class EmployeeLeaveAddedEvent : DomainEvent
{
    public Employee Employee { get; }
    public EmployeeLeave Leave { get; }
    public DateTime OccurredOn { get; }

    public EmployeeLeaveAddedEvent(Employee employee, EmployeeLeave leave)
    {
        Employee = employee;
        Leave = leave;
        OccurredOn = DateTime.UtcNow;
    }
}
