namespace Kantoku.Application.Dtos.Accounts;

/// <summary>
/// DTO for account response
/// </summary>
public class AccountDto
{
    public Guid Id { get; set; }
    public string LoginId { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string AccountType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public bool IsLocked { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public UserInfoDto? UserInfo { get; set; }
    public List<UserRoleDto> Roles { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// DTO for user information
/// </summary>
public class UserInfoDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? Phone { get; set; }
    public string? Gender { get; set; }
    public DateOnly? Birthday { get; set; }
    public string? AvatarUrl { get; set; }
}

/// <summary>
/// DTO for user role
/// </summary>
public class UserRoleDto
{
    public Guid Id { get; set; }
    public Guid RoleId { get; set; }
    public string RoleName { get; set; } = string.Empty;
    public Guid? OrganizationId { get; set; }
    public string? OrganizationName { get; set; }
    public DateTime AssignedAt { get; set; }
}

/// <summary>
/// DTO for role
/// </summary>
public class RoleDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public List<FunctionDto> Functions { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// DTO for function/permission
/// </summary>
public class FunctionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Category { get; set; } = string.Empty;
}
