using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.EmployeeManagement.Enums;

/// <summary>
/// Enumeration representing employee types
/// </summary>
public class EmployeeType : Enumeration
{
    public static readonly EmployeeType OFFICER = new(1, nameof(OFFICER));
    public static readonly EmployeeType WORKER = new(2, nameof(WORKER));
    public static readonly EmployeeType CONTRACTOR = new(3, nameof(CONTRACTOR));
    public static readonly EmployeeType INTERN = new(4, nameof(INTERN));
    public static readonly EmployeeType PART_TIME = new(5, nameof(PART_TIME));

    private EmployeeType(int id, string name) : base(id, name)
    {
    }

    public static IEnumerable<EmployeeType> GetAll()
    {
        return new[] { OFFICER, WORKER, CONTRACTOR, INTERN, PART_TIME };
    }

    public static EmployeeType FromName(string name)
    {
        var type = GetAll().FirstOrDefault(t => string.Equals(t.Name, name, StringComparison.OrdinalIgnoreCase));
        if (type == null)
            throw new ArgumentException($"Unknown employee type: {name}");
        return type;
    }

    public static EmployeeType FromId(int id)
    {
        var type = GetAll().FirstOrDefault(t => t.Id == id);
        if (type == null)
            throw new ArgumentException($"Unknown employee type ID: {id}");
        return type;
    }

    public bool IsFullTime => this == OFFICER || this == WORKER;
    public bool IsTemporary => this == CONTRACTOR || this == INTERN || this == PART_TIME;
}
