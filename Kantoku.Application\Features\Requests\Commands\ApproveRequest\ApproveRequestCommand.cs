using Kantoku.Application.Common.Models;
using Kantoku.Application.Dtos.Requests;
using Kantoku.Application.Interfaces;

namespace Kantoku.Application.Features.Requests.Commands.ApproveRequest;

/// <summary>
/// Command to approve or reject a request
/// </summary>
public class ApproveRequestCommand : ICommand<BaseResponse<RequestDto>>
{
    public Guid RequestId { get; set; }
    public Guid ApproverId { get; set; }
    public bool IsApproved { get; set; }
    public string? Comments { get; set; }
}
