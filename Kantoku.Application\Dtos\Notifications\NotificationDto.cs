namespace Kantoku.Application.Dtos.Notifications;

/// <summary>
/// DTO for notification response
/// </summary>
public class NotificationDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? ScheduledAt { get; set; }
    public DateTime? SentAt { get; set; }
    public List<NotificationTargetDto> Targets { get; set; } = new();
    public List<string> DeliveryChannels { get; set; } = new();
    public NotificationContentDto? Content { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// DTO for notification target
/// </summary>
public class NotificationTargetDto
{
    public Guid Id { get; set; }
    public string TargetType { get; set; } = string.Empty;
    public Guid TargetId { get; set; }
    public string? TargetName { get; set; }
    public bool IsRead { get; set; }
    public DateTime? ReadAt { get; set; }
    public bool IsDelivered { get; set; }
    public DateTime? DeliveredAt { get; set; }
}

/// <summary>
/// DTO for notification content
/// </summary>
public class NotificationContentDto
{
    public string? PlainText { get; set; }
    public string? Html { get; set; }
    public string? Markdown { get; set; }
    public string? TemplateName { get; set; }
    public Dictionary<string, object> TemplateData { get; set; } = new();
    public List<NotificationAttachmentDto> Attachments { get; set; } = new();
}

/// <summary>
/// DTO for notification attachment
/// </summary>
public class NotificationAttachmentDto
{
    public Guid Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long Size { get; set; }
    public string Url { get; set; } = string.Empty;
}
