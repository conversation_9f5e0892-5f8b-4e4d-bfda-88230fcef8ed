using Kantoku.Application.Dtos.Requests;
using Kantoku.Domain.RequestManagement;

namespace Kantoku.Application.Common.Mappings;

/// <summary>
/// Manual mapper for Request domain entity to DTOs
/// </summary>
public static class RequestMapper
{
    /// <summary>
    /// Maps Request domain entity to RequestDto
    /// </summary>
    /// <param name="request">Request domain entity</param>
    /// <returns>RequestDto</returns>
    public static RequestDto ToDto(this Request request)
    {
        return new RequestDto
        {
            Id = request.Id.Value,
            EmployeeId = request.EmployeeId.Value,
            RequestType = request.RequestType.Name,
            Title = request.Title,
            Description = request.Description,
            Priority = request.Priority.Name,
            Status = request.Status.Name,
            StartDate = request.RequestDates?.StartDate,
            EndDate = request.RequestDates?.EndDate,
            Amount = request.RequestDetails.Amount?.Amount,
            Currency = request.RequestDetails.Amount?.Currency,
            AdditionalDetails = request.RequestDetails.AdditionalDetails,
            Approvals = request.Approvals.Select(a => a.ToDto()).ToList(),
            CreatedAt = request.CreatedAt,
            UpdatedAt = request.UpdatedAt
        };
    }

    /// <summary>
    /// Maps RequestApproval domain entity to RequestApprovalDto
    /// </summary>
    /// <param name="approval">RequestApproval domain entity</param>
    /// <returns>RequestApprovalDto</returns>
    public static RequestApprovalDto ToDto(this RequestApproval approval)
    {
        return new RequestApprovalDto
        {
            Id = approval.Id.Value,
            ApproverId = approval.ApproverId,
            Order = approval.Order,
            Status = approval.Status.Name,
            Comments = approval.Comments,
            ApprovedAt = approval.ApprovedAt
        };
    }

    /// <summary>
    /// Maps a collection of Request entities to DTOs
    /// </summary>
    /// <param name="requests">Collection of Request entities</param>
    /// <returns>Collection of RequestDto</returns>
    public static IEnumerable<RequestDto> ToDto(this IEnumerable<Request> requests)
    {
        return requests.Select(req => req.ToDto());
    }

    /// <summary>
    /// Maps a list of Request entities to DTOs
    /// </summary>
    /// <param name="requests">List of Request entities</param>
    /// <returns>List of RequestDto</returns>
    public static List<RequestDto> ToDto(this List<Request> requests)
    {
        return requests.Select(req => req.ToDto()).ToList();
    }

    /// <summary>
    /// Maps a readonly list of Request entities to DTOs
    /// </summary>
    /// <param name="requests">Readonly list of Request entities</param>
    /// <returns>Readonly list of RequestDto</returns>
    public static IReadOnlyList<RequestDto> ToDto(this IReadOnlyList<Request> requests)
    {
        return requests.Select(req => req.ToDto()).ToList().AsReadOnly();
    }

    /// <summary>
    /// Maps Request domain entity to a simplified DTO for lists
    /// </summary>
    /// <param name="request">Request domain entity</param>
    /// <returns>Simplified RequestDto</returns>
    public static RequestDto ToSimpleDto(this Request request)
    {
        return new RequestDto
        {
            Id = request.Id.Value,
            EmployeeId = request.EmployeeId.Value,
            RequestType = request.RequestType.Name,
            Title = request.Title,
            Priority = request.Priority.Name,
            Status = request.Status.Name,
            StartDate = request.RequestDates?.StartDate,
            EndDate = request.RequestDates?.EndDate,
            Amount = request.RequestDetails.Amount?.Amount,
            Currency = request.RequestDetails.Amount?.Currency,
            CreatedAt = request.CreatedAt
        };
    }

    /// <summary>
    /// Maps a collection of Request entities to simplified DTOs
    /// </summary>
    /// <param name="requests">Collection of Request entities</param>
    /// <returns>Collection of simplified RequestDto</returns>
    public static IEnumerable<RequestDto> ToSimpleDto(this IEnumerable<Request> requests)
    {
        return requests.Select(req => req.ToSimpleDto());
    }
}
