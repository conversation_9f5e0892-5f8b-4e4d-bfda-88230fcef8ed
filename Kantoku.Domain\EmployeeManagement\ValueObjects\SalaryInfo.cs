using Kantoku.SharedKernel;
using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.EmployeeManagement.ValueObjects;

/// <summary>
/// Value object representing employee salary information
/// </summary>
public class SalaryInfo : ValueObject
{
    public int SalaryInMonth { get; private set; }
    public int SalaryInDay { get; private set; }
    public int SalaryInHour { get; private set; }
    public string Currency { get; private set; } = "JPY";

    private SalaryInfo() { } // For EF Core

    public SalaryInfo(
        int salaryInMonth,
        int salaryInDay = 0,
        int salaryInHour = 0,
        string currency = "JPY")
    {
        SetSalaryInMonth(salaryInMonth);
        SetSalaryInDay(salaryInDay);
        SetSalaryInHour(salaryInHour);
        SetCurrency(currency);
    }

    public static SalaryInfo CreateMonthly(int monthlyAmount, string currency = "JPY")
    {
        return new SalaryInfo(monthlyAmount, 0, 0, currency);
    }

    public static SalaryInfo CreateDaily(int dailyAmount, string currency = "JPY")
    {
        return new SalaryInfo(0, dailyAmount, 0, currency);
    }

    public static SalaryInfo CreateHourly(int hourlyAmount, string currency = "JPY")
    {
        return new SalaryInfo(0, 0, hourlyAmount, currency);
    }

    public static SalaryInfo CreateCombined(
        int monthlyAmount,
        int dailyAmount,
        int hourlyAmount,
        string currency = "JPY")
    {
        return new SalaryInfo(monthlyAmount, dailyAmount, hourlyAmount, currency);
    }

    /// <summary>
    /// Calculates estimated monthly salary based on daily/hourly rates
    /// </summary>
    public decimal CalculateEstimatedMonthlySalary(int workingDaysPerMonth = 22, float workingHoursPerDay = 8.0f)
    {
        decimal total = SalaryInMonth;

        if (SalaryInDay > 0)
            total += SalaryInDay * workingDaysPerMonth;

        if (SalaryInHour > 0)
            total += (decimal)(SalaryInHour * workingHoursPerDay * workingDaysPerMonth);

        return total;
    }

    /// <summary>
    /// Checks if this is a monthly salary structure
    /// </summary>
    public bool IsMonthlySalary => SalaryInMonth > 0 && SalaryInDay == 0 && SalaryInHour == 0;

    /// <summary>
    /// Checks if this is a daily salary structure
    /// </summary>
    public bool IsDailySalary => SalaryInMonth == 0 && SalaryInDay > 0 && SalaryInHour == 0;

    /// <summary>
    /// Checks if this is an hourly salary structure
    /// </summary>
    public bool IsHourlySalary => SalaryInMonth == 0 && SalaryInDay == 0 && SalaryInHour > 0;

    /// <summary>
    /// Checks if this is a combined salary structure
    /// </summary>
    public bool IsCombinedSalary => (SalaryInMonth > 0 ? 1 : 0) + (SalaryInDay > 0 ? 1 : 0) + (SalaryInHour > 0 ? 1 : 0) > 1;

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return SalaryInMonth;
        yield return SalaryInDay;
        yield return SalaryInHour;
        yield return Currency;
    }

    private void SetSalaryInMonth(int salaryInMonth)
    {
        if (salaryInMonth < 0)
            throw new ArgumentException("Monthly salary cannot be negative", nameof(salaryInMonth));

        SalaryInMonth = salaryInMonth;
    }

    private void SetSalaryInDay(int salaryInDay)
    {
        if (salaryInDay < 0)
            throw new ArgumentException("Daily salary cannot be negative", nameof(salaryInDay));

        SalaryInDay = salaryInDay;
    }

    private void SetSalaryInHour(int salaryInHour)
    {
        if (salaryInHour < 0)
            throw new ArgumentException("Hourly salary cannot be negative", nameof(salaryInHour));

        SalaryInHour = salaryInHour;
    }

    private void SetCurrency(string currency)
    {
        if (string.IsNullOrWhiteSpace(currency))
            throw new ArgumentException("Currency cannot be null or empty", nameof(currency));

        if (currency.Length != 3)
            throw new ArgumentException("Currency must be a 3-character ISO code", nameof(currency));

        Currency = currency.ToUpperInvariant();
    }

    public override string ToString()
    {
        var parts = new List<string>();

        if (SalaryInMonth > 0)
            parts.Add($"{SalaryInMonth:N0} {Currency}/month");

        if (SalaryInDay > 0)
            parts.Add($"{SalaryInDay:N0} {Currency}/day");

        if (SalaryInHour > 0)
            parts.Add($"{SalaryInHour:N0} {Currency}/hour");

        return string.Join(", ", parts);
    }
}
