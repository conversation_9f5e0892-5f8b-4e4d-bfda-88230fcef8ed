using Kantoku.Application.Common.Models;
using Kantoku.Application.Dtos.Projects;
using Kantoku.Application.Interfaces;

namespace Kantoku.Application.Features.Projects.Queries.GetProjectsByFilter;

/// <summary>
/// Query to get projects by filter with pagination
/// </summary>
public class GetProjectsByFilterQuery : IQuery<BaseResponse<PaginatedResponse<ProjectDto>>>
{
    public Guid? OrganizationId { get; set; }
    public string? SearchTerm { get; set; }
    public string? Status { get; set; }
    public Guid? CustomerId { get; set; }
    public Guid? ContractorId { get; set; }
    public Guid? ProjectTypeId { get; set; }
    public Guid? ManagerId { get; set; }
    public DateOnly? StartDateFrom { get; set; }
    public DateOnly? StartDateTo { get; set; }
    public DateOnly? EndDateFrom { get; set; }
    public DateOnly? EndDateTo { get; set; }
    public decimal? BudgetFrom { get; set; }
    public decimal? BudgetTo { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = false;
}
