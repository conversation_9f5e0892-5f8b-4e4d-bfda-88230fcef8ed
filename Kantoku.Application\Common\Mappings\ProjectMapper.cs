using Kantoku.Application.Dtos.Projects;
using Kantoku.Domain.ProjectManagement;

namespace Kantoku.Application.Common.Mappings;

/// <summary>
/// Manual mapper for Project domain entity to DTOs
/// </summary>
public static class ProjectMapper
{
    /// <summary>
    /// Maps Project domain entity to ProjectDto
    /// </summary>
    /// <param name="project">Project domain entity</param>
    /// <returns>ProjectDto</returns>
    public static ProjectDto ToDto(this Project project)
    {
        return new ProjectDto
        {
            Id = project.Id.Value,
            OrganizationId = project.OrganizationId.Value,
            ProjectCode = project.ProjectCode,
            Name = project.Name,
            Description = project.Description,
            ExpectedStartDate = project.ProjectDates.ExpectedStartDate,
            ExpectedEndDate = project.ProjectDates.ExpectedEndDate,
            ActualStartDate = project.ProjectDates.ActualStartDate,
            ActualEndDate = project.ProjectDates.ActualEndDate,
            EstimatedBudget = project.Budget?.EstimatedAmount?.Amount,
            ActualCost = project.Budget?.ActualAmount?.Amount,
            Status = project.Status.Name,
            CustomerId = project.CustomerId?.Value,
            ContractorId = project.ContractorId?.Value,
            ProjectTypeId = project.ProjectTypeId?.Value,
            ManagerIds = project.Managers.Select(m => m.EmployeeId).ToList(),
            CreatedAt = project.CreatedAt,
            UpdatedAt = project.UpdatedAt
        };
    }

    /// <summary>
    /// Maps a collection of Project entities to DTOs
    /// </summary>
    /// <param name="projects">Collection of Project entities</param>
    /// <returns>Collection of ProjectDto</returns>
    public static IEnumerable<ProjectDto> ToDto(this IEnumerable<Project> projects)
    {
        return projects.Select(proj => proj.ToDto());
    }

    /// <summary>
    /// Maps a list of Project entities to DTOs
    /// </summary>
    /// <param name="projects">List of Project entities</param>
    /// <returns>List of ProjectDto</returns>
    public static List<ProjectDto> ToDto(this List<Project> projects)
    {
        return projects.Select(proj => proj.ToDto()).ToList();
    }

    /// <summary>
    /// Maps a readonly list of Project entities to DTOs
    /// </summary>
    /// <param name="projects">Readonly list of Project entities</param>
    /// <returns>Readonly list of ProjectDto</returns>
    public static IReadOnlyList<ProjectDto> ToDto(this IReadOnlyList<Project> projects)
    {
        return projects.Select(proj => proj.ToDto()).ToList().AsReadOnly();
    }

    /// <summary>
    /// Maps Project domain entity to a simplified DTO for lists
    /// </summary>
    /// <param name="project">Project domain entity</param>
    /// <returns>Simplified ProjectDto</returns>
    public static ProjectDto ToSimpleDto(this Project project)
    {
        return new ProjectDto
        {
            Id = project.Id.Value,
            ProjectCode = project.ProjectCode,
            Name = project.Name,
            Status = project.Status.Name,
            ExpectedStartDate = project.ProjectDates.ExpectedStartDate,
            ExpectedEndDate = project.ProjectDates.ExpectedEndDate,
            EstimatedBudget = project.Budget?.EstimatedAmount?.Amount,
            CustomerId = project.CustomerId?.Value,
            ContractorId = project.ContractorId?.Value
        };
    }

    /// <summary>
    /// Maps a collection of Project entities to simplified DTOs
    /// </summary>
    /// <param name="projects">Collection of Project entities</param>
    /// <returns>Collection of simplified ProjectDto</returns>
    public static IEnumerable<ProjectDto> ToSimpleDto(this IEnumerable<Project> projects)
    {
        return projects.Select(proj => proj.ToSimpleDto());
    }
}
