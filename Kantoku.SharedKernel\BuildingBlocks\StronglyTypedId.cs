namespace Kantoku.SharedKernel.BuildingBlocks;

/// <summary>
/// Base class for strongly-typed identifiers.
/// Provides type safety and prevents mixing different types of IDs.
/// </summary>
/// <typeparam name="T">The type that this ID represents</typeparam>
public abstract class StronglyTypedId<T> : ValueObject, IEquatable<T>
    where T : StronglyTypedId<T>
{
    /// <summary>
    /// Gets the underlying Guid value
    /// </summary>
    public Guid Value { get; }

    /// <summary>
    /// Initializes a new instance with the specified Guid value
    /// </summary>
    /// <param name="value">The Guid value</param>
    /// <exception cref="ArgumentException">Thrown when the value is empty</exception>
    protected StronglyTypedId(Guid value)
    {
        if (value == Guid.Empty)
        {
            throw new ArgumentException("ID cannot be empty", nameof(value));
        }
        Value = value;
    }

    /// <summary>
    /// Creates a new instance with a new Guid
    /// </summary>
    /// <returns>A new strongly-typed ID</returns>
    public static T New() => (T)Activator.CreateInstance(typeof(T), Guid.NewGuid())!;

    /// <summary>
    /// Creates an instance from a Guid value
    /// </summary>
    /// <param name="value">The Guid value</param>
    /// <returns>A strongly-typed ID</returns>
    public static T From(Guid value) => (T)Activator.CreateInstance(typeof(T), value)!;

    /// <summary>
    /// Implicit conversion to Guid
    /// </summary>
    /// <param name="id">The strongly-typed ID</param>
    public static implicit operator Guid(StronglyTypedId<T> id) => id.Value;

    /// <summary>
    /// Determines whether this ID is equal to another ID of the same type
    /// </summary>
    /// <param name="other">The other ID</param>
    /// <returns>True if the IDs are equal; otherwise, false</returns>
    public bool Equals(T? other) => other is not null && Value.Equals(other.Value);

    /// <summary>
    /// Returns the string representation of the ID
    /// </summary>
    /// <returns>The string representation of the underlying Guid</returns>
    public override string ToString() => Value.ToString();

    /// <summary>
    /// Gets the equality components for value object comparison
    /// </summary>
    /// <returns>The Guid value as the equality component</returns>
    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Value;
    }
}
