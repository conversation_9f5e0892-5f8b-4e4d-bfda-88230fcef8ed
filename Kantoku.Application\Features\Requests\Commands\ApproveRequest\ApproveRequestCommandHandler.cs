using Kantoku.Application.Common.Mappings;
using Kantoku.Application.Common.Models;
using Kantoku.Application.Dtos.Requests;
using Kantoku.Application.Interfaces;
using Kantoku.Application.Services;
using Kantoku.Domain.RequestManagement;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Kantoku.Application.Features.Requests.Commands.ApproveRequest;

/// <summary>
/// Handler for ApproveRequestCommand
/// </summary>
public class ApproveRequestCommandHandler : IRequestHandler<ApproveRequestCommand, BaseResponse<RequestDto>>
{
    private readonly IRequestRepository _requestRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ApproveRequestCommandHandler> _logger;
    private readonly INotificationService _notificationService;

    public ApproveRequestCommandHandler(
        IRequestRepository requestRepository,
        IUnitOfWork unitOfWork,
        ILogger<ApproveRequestCommandHandler> logger,
        INotificationService notificationService)
    {
        _requestRepository = requestRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _notificationService = notificationService;
    }

    public async Task<BaseResponse<RequestDto>> Handle(ApproveRequestCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var requestId = RequestId.From(request.RequestId);
            var requestEntity = await _requestRepository.GetByIdAsync(requestId, cancellationToken);

            if (requestEntity == null)
            {
                _logger.LogWarning("Request not found with ID: {RequestId}", request.RequestId);
                return BaseResponse<RequestDto>.Failure("Request not found");
            }

            // Process the approval
            if (request.IsApproved)
            {
                requestEntity.Approve(request.ApproverId, request.Comments);
            }
            else
            {
                requestEntity.Reject(request.ApproverId, request.Comments);
            }

            // Save changes
            await _requestRepository.UpdateAsync(requestEntity, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Send notification to requester
            var notificationTitle = request.IsApproved ? "Request Approved" : "Request Rejected";
            var notificationMessage = request.IsApproved
                ? $"Your request '{requestEntity.Title}' has been approved."
                : $"Your request '{requestEntity.Title}' has been rejected. Reason: {request.Comments}";

            await _notificationService.SendNotificationAsync(
                requestEntity.EmployeeId.Value,
                notificationTitle,
                notificationMessage,
                request.IsApproved ? NotificationType.Success : NotificationType.Warning,
                NotificationPriority.Normal,
                new[] { DeliveryChannel.InApp, DeliveryChannel.Email },
                cancellationToken: cancellationToken);

            // Map to DTO using manual mapper
            var requestDto = requestEntity.ToDto();

            _logger.LogInformation("Request {RequestId} {Action} by {ApproverId}",
                request.RequestId,
                request.IsApproved ? "approved" : "rejected",
                request.ApproverId);

            return BaseResponse<RequestDto>.Success(requestDto,
                request.IsApproved ? "Request approved successfully" : "Request rejected successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing request approval: {ErrorMessage}", ex.Message);
            return BaseResponse<RequestDto>.Failure("Failed to process request approval");
        }
    }
}
