using Kantoku.Application.Common.Models;
using Kantoku.Application.Dtos.Requests;
using Kantoku.Application.Interfaces;

namespace Kantoku.Application.Features.Requests.Commands.CreateRequest;

/// <summary>
/// Command to create a new request
/// </summary>
public class CreateRequestCommand : ICommand<BaseResponse<RequestDto>>
{
    public Guid EmployeeId { get; set; }
    public string RequestType { get; set; } = string.Empty; // Leave, Overtime, Equipment, Travel, Training, Expense, Other
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Priority { get; set; } = "Normal"; // Low, Normal, High, Urgent
    public DateOnly? StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
    public decimal? Amount { get; set; }
    public string? Currency { get; set; } = "JPY";
    public Dictionary<string, object> AdditionalDetails { get; set; } = new();
    public List<Guid> ApproverIds { get; set; } = new();
}
