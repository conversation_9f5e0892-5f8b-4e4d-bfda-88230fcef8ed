using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.Common.Auditing
{
    public abstract class FullAuditedEntity<TId> : Entity<TId>, IFullAudited
        where TId : class, IEquatable<TId>
    {
        protected FullAuditedEntity(TId id) : base(id) { }

        // ICreationAudited
        public DateTime CreatedAt { get; set; }
        public Guid? CreatedBy { get; set; }

        // IModificationAudited
        public DateTime? LastModifiedAt { get; set; }
        public Guid? LastModifiedBy { get; set; }

        // ISoftDeleteAudited
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public Guid? DeletedBy { get; set; }
    }
}