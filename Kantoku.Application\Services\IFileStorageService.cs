namespace Kantoku.Application.Services;

/// <summary>
/// Interface for file storage service
/// </summary>
public interface IFileStorageService
{
    /// <summary>
    /// Uploads a file asynchronously
    /// </summary>
    /// <param name="fileName">Name of the file</param>
    /// <param name="content">File content as byte array</param>
    /// <param name="contentType">MIME type of the file</param>
    /// <param name="folder">Optional folder path</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File URL or path</returns>
    Task<string> UploadFileAsync(string fileName, byte[] content, string contentType, string? folder = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Uploads a file from stream asynchronously
    /// </summary>
    /// <param name="fileName">Name of the file</param>
    /// <param name="stream">File stream</param>
    /// <param name="contentType">MIME type of the file</param>
    /// <param name="folder">Optional folder path</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File URL or path</returns>
    Task<string> UploadFileAsync(string fileName, Stream stream, string contentType, string? folder = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Downloads a file asynchronously
    /// </summary>
    /// <param name="filePath">Path to the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File content as byte array</returns>
    Task<byte[]> DownloadFileAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Downloads a file as stream asynchronously
    /// </summary>
    /// <param name="filePath">Path to the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File stream</returns>
    Task<Stream> DownloadFileStreamAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a file asynchronously
    /// </summary>
    /// <param name="filePath">Path to the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if file was deleted successfully</returns>
    Task<bool> DeleteFileAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a file exists
    /// </summary>
    /// <param name="filePath">Path to the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if file exists</returns>
    Task<bool> FileExistsAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets file metadata
    /// </summary>
    /// <param name="filePath">Path to the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File metadata</returns>
    Task<FileMetadata?> GetFileMetadataAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates a pre-signed URL for file access
    /// </summary>
    /// <param name="filePath">Path to the file</param>
    /// <param name="expirationTime">URL expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Pre-signed URL</returns>
    Task<string> GeneratePreSignedUrlAsync(string filePath, TimeSpan expirationTime, CancellationToken cancellationToken = default);
}

/// <summary>
/// File metadata model
/// </summary>
public class FileMetadata
{
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long Size { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastModified { get; set; }
    public string ETag { get; set; } = string.Empty;
}
