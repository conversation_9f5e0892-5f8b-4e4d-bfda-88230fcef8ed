using Kantoku.Application.Dtos.Employees;
using Kantoku.Domain.EmployeeManagement;

namespace Kantoku.Application.Common.Mappings;

/// <summary>
/// Manual mapper for Employee domain entity to DTOs
/// </summary>
public static class EmployeeMapper
{
    /// <summary>
    /// Maps Employee domain entity to EmployeeDto
    /// </summary>
    /// <param name="employee">Employee domain entity</param>
    /// <returns>EmployeeDto</returns>
    public static EmployeeDto ToDto(this Employee employee)
    {
        return new EmployeeDto
        {
            Id = employee.Id.Value,
            AccountId = employee.AccountId.Value,
            OrganizationId = employee.OrganizationId.Value,
            EmployeeCode = employee.EmployeeCode,
            FirstName = employee.EmployeeName.FirstName,
            LastName = employee.EmployeeName.LastName,
            MiddleName = employee.EmployeeName.MiddleName,
            FullName = employee.EmployeeName.FullName,
            MonthlySalary = employee.SalaryInfo.MonthlySalary,
            DailySalary = employee.SalaryInfo.DailySalary,
            HourlySalary = employee.SalaryInfo.HourlySalary,
            EmployeeType = employee.EmployeeType.Name,
            WorkingStatus = employee.WorkingStatus.Name,
            StructureId = employee.StructureId?.Value,
            PositionId = employee.PositionId?.Value,
            RankingId = employee.RankingId?.Value,
            Email = employee.ContactInfo?.PrimaryEmail,
            Phone = employee.ContactInfo?.PrimaryPhone,
            IsOrgAdmin = employee.IsOrgAdmin,
            IsOrgOwner = employee.IsOrgOwner,
            HasApprovalAuthority = employee.HasApprovalAuthority,
            CreatedAt = employee.CreatedAt,
            UpdatedAt = employee.UpdatedAt
        };
    }

    /// <summary>
    /// Maps a collection of Employee entities to DTOs
    /// </summary>
    /// <param name="employees">Collection of Employee entities</param>
    /// <returns>Collection of EmployeeDto</returns>
    public static IEnumerable<EmployeeDto> ToDto(this IEnumerable<Employee> employees)
    {
        return employees.Select(emp => emp.ToDto());
    }

    /// <summary>
    /// Maps a list of Employee entities to DTOs
    /// </summary>
    /// <param name="employees">List of Employee entities</param>
    /// <returns>List of EmployeeDto</returns>
    public static List<EmployeeDto> ToDto(this List<Employee> employees)
    {
        return employees.Select(emp => emp.ToDto()).ToList();
    }

    /// <summary>
    /// Maps a readonly list of Employee entities to DTOs
    /// </summary>
    /// <param name="employees">Readonly list of Employee entities</param>
    /// <returns>Readonly list of EmployeeDto</returns>
    public static IReadOnlyList<EmployeeDto> ToDto(this IReadOnlyList<Employee> employees)
    {
        return employees.Select(emp => emp.ToDto()).ToList().AsReadOnly();
    }

    /// <summary>
    /// Maps Employee domain entity to a simplified DTO for lists
    /// </summary>
    /// <param name="employee">Employee domain entity</param>
    /// <returns>Simplified EmployeeDto</returns>
    public static EmployeeDto ToSimpleDto(this Employee employee)
    {
        return new EmployeeDto
        {
            Id = employee.Id.Value,
            EmployeeCode = employee.EmployeeCode,
            FirstName = employee.EmployeeName.FirstName,
            LastName = employee.EmployeeName.LastName,
            FullName = employee.EmployeeName.FullName,
            EmployeeType = employee.EmployeeType.Name,
            WorkingStatus = employee.WorkingStatus.Name,
            Email = employee.ContactInfo?.PrimaryEmail,
            Phone = employee.ContactInfo?.PrimaryPhone,
            IsOrgAdmin = employee.IsOrgAdmin,
            HasApprovalAuthority = employee.HasApprovalAuthority
        };
    }

    /// <summary>
    /// Maps a collection of Employee entities to simplified DTOs
    /// </summary>
    /// <param name="employees">Collection of Employee entities</param>
    /// <returns>Collection of simplified EmployeeDto</returns>
    public static IEnumerable<EmployeeDto> ToSimpleDto(this IEnumerable<Employee> employees)
    {
        return employees.Select(emp => emp.ToSimpleDto());
    }
}
