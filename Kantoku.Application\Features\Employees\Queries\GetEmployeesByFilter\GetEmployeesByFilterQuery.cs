using Kantoku.Application.Common.Models;
using Kantoku.Application.Dtos.Employees;
using Kantoku.Application.Interfaces;

namespace Kantoku.Application.Features.Employees.Queries.GetEmployeesByFilter;

/// <summary>
/// Query to get employees by filter with pagination
/// </summary>
public class GetEmployeesByFilterQuery : IQuery<BaseResponse<PaginatedResponse<EmployeeDto>>>
{
    public Guid? OrganizationId { get; set; }
    public string? SearchTerm { get; set; }
    public string? EmployeeType { get; set; }
    public string? WorkingStatus { get; set; }
    public Guid? StructureId { get; set; }
    public Guid? PositionId { get; set; }
    public Guid? RankingId { get; set; }
    public bool? IsOrgAdmin { get; set; }
    public bool? HasApprovalAuthority { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = false;
}
